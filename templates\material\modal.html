{% comment %}
Material Design M2 Reusable Modal Component
Compatible with HTMX and Django forms

Usage:
{% include 'material/modal.html' with modal_id='student-modal' modal_title='Add Student' %}

Parameters:
- modal_id: Unique ID for the modal (required)
- modal_title: Title displayed in modal header (required)
- modal_size: 'small', 'medium', 'large', 'xl' (default: 'medium')
- modal_form_id: ID for the form inside modal (default: modal_id + '-form')
- submit_text: Text for submit button (default: 'Save')
- cancel_text: Text for cancel button (default: 'Cancel')
- show_footer: Whether to show modal footer with buttons (default: true)
{% endcomment %}

{% load static %}

<!-- Modal Overlay -->
<div class="modal-overlay" id="modal-overlay">
    <class class="modal modal-form {% if modal_size == 'small' %}modal--small{% elif modal_size == 'large' %}modal--large{% elif modal_size == 'xl' %}modal--xl{% elif modal_size == 'xxl' %}modal--xxl{% endif %}"
        id="modal">
        <!-- Modal Header -->
        <div class="modal-header">
            <h2 class="modal-title" id="modal-title">{{ modal_title|default:"Modal" }}</h2>
            <button class="modal-close" id="modal-close" type="button">
                <span class="material-icons">close</span>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="modal-content" id="modal-content">
            <!-- Content will be loaded here via HTMX or populated directly -->
        </div>

        <!-- Modal Footer -->
        {% if not hide_footer %}
        <div class="modal-actions" id="modal-actions" hx-swap="outerHTML">
            <button type="button" class="mdc-button mdc-button--outlined modal-cancel" id="modal-cancel">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">{{ cancel_text|default:"Annuler" }}</span>
            </button>
            <button type="submit" class="mdc-button mdc-button--raised modal-submit" id="modal-submit"
                form="modal-form">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">{{ submit_text|default:"Enregistrer" }}</span>
            </button>
        </div>
        {% endif %}
    </class>
</div>

<script>
// Initialize Material Design components for generic modal
(function() {
    const modalCancel = document.getElementById('modal-cancel');
    const modalSubmit = document.getElementById('modal-submit');
    const modalClose = document.getElementById('modal-close');
    const modalOverlay = document.getElementById('modal-overlay');

    // Initialize MDC ripple for modal buttons
    function initializeModalButtons() {
        if (typeof mdc !== 'undefined' && mdc.ripple) {
            if (modalCancel && !modalCancel.mdcRipple) {
                modalCancel.mdcRipple = new mdc.ripple.MDCRipple(modalCancel);
            }
            if (modalSubmit && !modalSubmit.mdcRipple) {
                modalSubmit.mdcRipple = new mdc.ripple.MDCRipple(modalSubmit);
            }
        }
    }

    // After swap if the target is modal-content log modalSubmit
    document.addEventListener('htmx:afterSwap', function(event) {
        const target = event.detail.target;
        if (target.id === 'modal-content') {
            setTimeout(() => {
                modalSubmit.setAttribute('hx-post', document.getElementById('modal-form').getAttribute('action'));
                console.log(modalSubmit)
                console.log(document.getElementById('modal-form'))
            }, 50);
        }
    });


    // Initialize on load
    document.addEventListener('DOMContentLoaded', initializeModalButtons);

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const modalOverlay = document.getElementById('modal-overlay');
            if (modalOverlay && modalOverlay.classList.contains('active')) {
                if (window.MaterialModalHTMX) {
                    window.MaterialModalHTMX.hide('modal');
                    window.MaterialModalHTMX.clear('modal');
                } else {
                    // Fallback for generic modal system
                    modalOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }
        }
    });

    // Expose generic modal control functions globally
    window.genericModal = {
        show: () => {
            if (window.MaterialModalHTMX) {
                window.MaterialModalHTMX.show('modal');
            } else {
                modalOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        },
        hide: () => {
            if (window.MaterialModalHTMX) {
                window.MaterialModalHTMX.hide('modal');
                window.MaterialModalHTMX.clear('modal');
            } else {
                modalOverlay.classList.remove('active');
                document.body.style.overflow = '';
            }
        },
        setTitle: (title) => {
            const modalTitle = document.getElementById('modal-title');
            if (modalTitle) modalTitle.textContent = title;
        },
        setContent: (content) => {
            const modalContent = document.getElementById('modal-content');
            if (modalContent) modalContent.innerHTML = content;
        }
    };

    // Initialize Flatpickr for date fields in modal
    flatpickr("#modal-content [name*='date']", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "true",
        locale: "fr",
        maxDate: 'today'
    });
})();
</script>
